import subprocess
import time
import tkinter as tk
from tkinter import ttk
from pynput import keyboard
from pynput.keyboard import Key
import threading
import os
import base64
from pathlib import Path
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import tempfile
import shutil
from datetime import datetime
from PIL import Image
import win32clipboard
import struct


class ScreenshotManager:
    def __init__(self):
        self.hotkey_detected = False
        self.clear_hotkey_detected = False
        self.running = True
        self.hotkey_listener = None
        self.root = None

        # ShareX output directory - only monitor your specific path
        self.sharex_dir = Path("C:/Users/<USER>/Desktop/ShareX-18.0.1-portable/ShareX/Screenshots/2025-08")
        self.temp_dir = Path(tempfile.gettempdir()) / "screenshot_manager"
        self.temp_dir.mkdir(exist_ok=True)

        # File monitoring
        self.file_observer = None
        self.latest_file = None
        self.processing_lock = threading.Lock()
        self.collected_screenshots = []
        self.screenshot_lock = threading.Lock()

        # Create persistent root window
        self.setup_root()
        self.setup_file_monitoring()



    def setup_root(self):
        """Setup the persistent root window."""
        self.root = tk.Tk()
        self.root.withdraw()  # Hide the root window
        self.root.title("Screenshot Manager")

    def setup_file_monitoring(self):
        """Setup file system monitoring for ShareX output."""
        if not self.sharex_dir.exists():
            self.sharex_dir.mkdir(parents=True, exist_ok=True)
            print(f"[+] Created ShareX directory: {self.sharex_dir}")
        else:
            print(f"[OK] Found ShareX directory: {self.sharex_dir}")

        self.file_handler = ShareXFileHandler(self)
        self.file_observer = Observer()

        # Monitor only the specific ShareX directory
        self.file_observer.schedule(self.file_handler, str(self.sharex_dir), recursive=False)
        self.file_observer.start()



    def show_notification(self, message):
        """Show a simple notification message."""
        def create_notification():
            notification = tk.Toplevel(self.root)
            notification.title("Screenshot Manager")
            notification.overrideredirect(True)
            notification.attributes('-topmost', True)
            notification.attributes('-alpha', 0.9)

            # Main frame with styling
            outer_frame = tk.Frame(notification, bg='#2E86AB', relief='raised', bd=2)
            outer_frame.pack(padx=5, pady=5)
            inner_frame = tk.Frame(outer_frame, bg='#F18F01', relief='flat', bd=1)
            inner_frame.pack(padx=3, pady=3)

            # Message label
            message_label = tk.Label(
                inner_frame,
                text=message,
                font=('Arial', 12),
                fg='white',
                bg='#F18F01',
                padx=15,
                pady=10,
                justify='center'
            )
            message_label.pack()

            def close_notification():
                try:
                    notification.destroy()
                except:
                    pass

            # Update window and position it
            notification.update_idletasks()
            width = notification.winfo_reqwidth()
            height = notification.winfo_reqheight()
            
            # Position in top-right corner
            x = notification.winfo_screenwidth() - width - 20
            y = 20
            notification.geometry(f"{width}x{height}+{x}+{y}")

            # Auto-close after 3 seconds
            notification.after(3000, close_notification)

        if self.root:
            self.root.after(0, create_notification)

    def open_screenshots_folder(self):
        """Open the screenshots folder in explorer."""
        try:
            os.startfile(self.temp_dir)
            print(f"[OK] Opened screenshots folder: {self.temp_dir}")
        except Exception as e:
            print(f"[ERROR] Could not open folder: {e}")


    def on_screenshot_hotkey(self):
        """Handle Ctrl+Alt+S hotkey press for taking screenshots."""
        print("Ctrl+Alt+S detected!")
        self.hotkey_detected = True

    def on_clear_hotkey(self):
        """Handle Ctrl+Alt+X hotkey press for clearing screenshots."""
        print("Ctrl+Alt+X detected!")
        self.clear_hotkey_detected = True

    def check_hotkey(self):
        """Check for hotkey detection."""
        if self.hotkey_detected:
            self.hotkey_detected = False
            print("Launching ShareX for screenshot capture...")
            self.launch_sharex_capture()
        if self.clear_hotkey_detected:
            self.clear_hotkey_detected = False
            print("Clearing all screenshots...")
            self.clear_all_screenshots()
        if self.running:
            self.root.after(100, self.check_hotkey)

    def start_listener(self):
        """Start the keyboard listeners."""
        print("Screenshot Manager - ShareX Integration")
        print("=" * 40)
        print("Press Ctrl+Alt+S to capture screenshot")
        print("Press Ctrl+Alt+X to clear all screenshots")
        print("Press Ctrl+C to exit")
        print()
        print(f"[+] Monitoring ShareX directory: {self.sharex_dir}")
        print(f"[+] Screenshots stored in: {self.temp_dir}")
        print("Note: All screenshots are kept in clipboard until you press Ctrl+Alt+X to clear.")

        # Setup global hotkeys
        self.hotkey_listener = keyboard.GlobalHotKeys({
            '<ctrl>+<alt>+s': self.on_screenshot_hotkey,
            '<ctrl>+<alt>+x': self.on_clear_hotkey
        })

        self.hotkey_listener.start()

        # Start checking for hotkeys
        self.check_hotkey()

        # Start tkinter main loop
        try:
            self.root.mainloop()
        finally:
            if self.hotkey_listener:
                self.hotkey_listener.stop()
            if self.file_observer:
                self.file_observer.stop()
                self.file_observer.join()

    def launch_sharex_capture(self):
        """Launch ShareX region capture using CLI hotkey actions."""
        try:
            cmd = ['ShareX', '-RectangleRegion']
            subprocess.Popen(cmd)
            print("[OK] ShareX rectangle region capture launched")

        except FileNotFoundError:
            print("[ERROR] ShareX not found in PATH")
        except Exception as e:
            print(f"Error launching ShareX: {e}")


    def send_to_clipboard(self, image_paths):
        """Send multiple images to clipboard as file drop."""
        try:
            # Convert to list if single path provided
            if isinstance(image_paths, str):
                image_paths = [image_paths]
            
            # Create DROPFILES structure
            # Create a string with all paths separated by null characters
            files_str = "\0".join(image_paths) + "\0\0"  # Double null terminator
            
            # Create DROPFILES structure
            # DWORD fWide; (0 for ANSI, 1 for Unicode)
            # POINT pt; (0, 0)
            # DWORD fNC; (0)
            # DWORD fWide; (1 for Unicode)
            dropfiles = struct.pack("IiiII", 20, 0, 0, 0, 1) + files_str.encode('utf-16le')
            
            win32clipboard.OpenClipboard()
            win32clipboard.EmptyClipboard()
            win32clipboard.SetClipboardData(win32clipboard.CF_HDROP, dropfiles)
            win32clipboard.CloseClipboard()
            
            print(f"[OK] {len(image_paths)} image(s) sent to clipboard")
            return True
        except Exception as e:
            print(f"[ERROR] Error sending images to clipboard: {e}")
            return False

    def update_clipboard_with_all(self):
        """Update clipboard with all collected screenshots."""
        if self.collected_screenshots:
            self.send_to_clipboard(self.collected_screenshots)

    def process_new_image(self, image_path):
        """Process newly captured image by storing it and sending to clipboard."""
        with self.screenshot_lock:
            try:
                print(f"[+] New screenshot detected: {image_path}")
                time.sleep(0.5)
                if not os.path.exists(image_path):
                    print("[ERROR] Image file not found"); return
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
                temp_filename = f"screenshot_{len(self.collected_screenshots)+1:03d}_{timestamp}.png"
                temp_path = self.temp_dir / temp_filename
                shutil.copy2(image_path, temp_path)
                self.collected_screenshots.append(str(temp_path))
                
                # Update clipboard with all screenshots
                self.update_clipboard_with_all()
                
                # Show notification with total count
                self.show_notification(f"Screenshot captured! Total: {len(self.collected_screenshots)}")
                
                print(f"[OK] Screenshot {len(self.collected_screenshots)} stored. Press Ctrl+Alt+X to clear all screenshots.")
                try:
                    os.remove(image_path)
                    print(f"[DEL] Cleaned up original screenshot: {os.path.basename(image_path)}")
                except Exception as e:
                    print(f"[WARN] Could not delete original image: {e}")
            except Exception as e:
                print(f"[ERROR] Error storing image: {e}")
                if os.path.exists(image_path):
                    try: os.remove(image_path)
                    except: pass

    def clear_all_screenshots(self):
        """Clear all collected screenshots and clipboard."""
        with self.screenshot_lock:
            if not self.collected_screenshots:
                print("[ERROR] No screenshots to clear.")
                self.show_notification("No screenshots to clear!")
                return
            try:
                print(f"[+] Clearing {len(self.collected_screenshots)} screenshot(s)...")
                for screenshot_path in self.collected_screenshots:
                    try:
                        os.remove(screenshot_path)
                        print(f"[DEL] Cleaned up temp screenshot: {os.path.basename(screenshot_path)}")
                    except Exception as e:
                        print(f"[WARN] Could not delete temp image: {e}")
                self.collected_screenshots.clear()

                # Clear clipboard
                try:
                    win32clipboard.OpenClipboard()
                    win32clipboard.EmptyClipboard()
                    win32clipboard.CloseClipboard()
                    print("[OK] Clipboard cleared.")
                except Exception as e:
                    print(f"[WARN] Could not clear clipboard: {e}")

                print("[OK] All screenshots cleared.")
                self.show_notification("All screenshots cleared!")
            except Exception as e:
                print(f"[ERROR] Error clearing screenshots: {e}")
                # Try to clean up any remaining files
                for screenshot_path in self.collected_screenshots:
                    try: os.remove(screenshot_path)
                    except: pass
                self.collected_screenshots.clear()

    def process_collected_screenshots(self):
        """Process all collected screenshots at once."""
        with self.screenshot_lock:
            if not self.collected_screenshots:
                print("[ERROR] No screenshots collected. Take some screenshots first with Ctrl+Alt+S.")
                return
            try:
                print(f"[+] Processing {len(self.collected_screenshots)} screenshot(s)...")
                # For now, just clear them
                for screenshot_path in self.collected_screenshots:
                    try:
                        os.remove(screenshot_path)
                        print(f"[DEL] Cleaned up temp screenshot: {os.path.basename(screenshot_path)}")
                    except Exception as e:
                        print(f"[WARN] Could not delete temp image: {e}")
                self.collected_screenshots.clear()
                print("[OK] Ready for next question sequence (press Ctrl+Alt+S to start capturing)")
            except Exception as e:
                print(f"[ERROR] Error processing collected screenshots: {e}")
                for screenshot_path in self.collected_screenshots:
                    try: os.remove(screenshot_path)
                    except: pass
                self.collected_screenshots.clear()


class ShareXFileHandler(FileSystemEventHandler):
    """File system event handler for ShareX screenshots."""

    def __init__(self, manager):
        self.manager = manager
        self.last_processed = None

    def on_created(self, event):
        if event.is_directory:
            return

        file_path = event.src_path

        if file_path.lower().endswith(('.png', '.jpg', '.jpeg')):
            if file_path != self.last_processed:
                self.last_processed = file_path
                threading.Thread(
                    target=self.manager.process_new_image,
                    args=(file_path,),
                    daemon=True
                ).start()


def main():
    """Main function."""
    try:
        print("Screenshot Manager - ShareX Integration")
        print("=" * 40)

        try:
            import watchdog, PIL, win32clipboard
            print("[OK] All dependencies found")
        except ImportError as e:
            print(f"[ERROR] Missing dependency: {e.name}")
            print("Please run: pip install watchdog pillow pywin32 pynput")
            return

        try:
            subprocess.run(['ShareX', '--version'], capture_output=True, timeout=3, check=True)
            print("[OK] ShareX found and accessible")
        except Exception:
            print("[WARN] ShareX may not be in PATH or is not installed.")

        print()

        manager = ScreenshotManager()
        manager.start_listener()

    except KeyboardInterrupt:
        print("\nExiting...")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")


if __name__ == "__main__":
    main()